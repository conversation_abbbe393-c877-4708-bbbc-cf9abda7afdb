defmodule Repobot.Repositories do
  @moduledoc """
  The Repositories context.
  """

  require Logger

  import Ecto.Query, warn: false

  alias Repobot.Repo
  alias Repobot.{Repository, SourceFile, RepositorySourceFile, Folder}
  alias Repobot.RepositoryFiles

  @doc """
  Returns the list of repositories.
  """
  def list_repositories do
    Repository
    |> preload([:source_files, :files])
    |> Repo.all()
  end

  @doc """
  Searches repositories by full name for a given user.
  Returns repositories whose full name contains the search term (case insensitive).
  """
  def search_repositories(user, search_term) when is_binary(search_term) and search_term != "" do
    search_term = "%#{search_term}%"

    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    Repository
    |> where(owner: ^user.login)
    |> where([r], ilike(r.full_name, ^search_term))
    |> preload([:source_files])
    |> Repo.all()
    |> Repo.preload(files: files_query)
  end

  def search_repositories(user, _), do: user_repositories(user)

  @doc """
  Gets a single repository.
  Raises `Ecto.NoResultsError` if the Repository does not exist.
  """
  def get_repository!(id) do
    # Define custom query for imported_files with content projection
    imported_files_query =
      from(sf in Repobot.SourceFile,
        left_join: fc in assoc(sf, :file_content),
        select: %{sf | content: fc.blob}
      )

    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    Repository
    |> Repo.get!(id)
    |> Repo.preload([:source_files])
    |> Repo.preload(files: files_query)
    |> Repo.preload(imported_files: imported_files_query)
  end

  @doc """
  Gets repository files at a specific path.
  """
  def get_repository_files!(id, _user, path \\ "/") do
    repository = get_repository!(id)
    RepositoryFiles.get_files_at_path(repository, path)
  end

  @doc """
  Refreshes repository files by syncing with GitHub.
  """
  def refresh_repository_files!(id, user \\ nil) do
    repository = get_repository!(id)

    case RepositoryFiles.sync_repository_files(repository, user) do
      {:ok, _files} ->
        # Define custom query for files with content projection
        files_query =
          from(rf in Repobot.RepositoryFile,
            left_join: fc in assoc(rf, :file_content),
            select: %{rf | content: fc.blob}
          )

        repository = repository |> Repo.preload(files: files_query, force: true)
        {:ok, repository}

      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  Creates a repository.
  """
  def create_repository(attrs \\ %{}) do
    %Repository{}
    |> Repository.changeset(attrs)
    |> Repo.insert()
  end

  @doc """
  Updates a repository.
  """
  def update_repository(%Repository{} = repository, attrs) do
    repository
    |> Repository.changeset(attrs)
    |> Repo.update()
  end

  @doc """
  Deletes a repository.
  """
  def delete_repository(%Repository{} = repository) do
    Repo.delete(repository)
  end

  @doc """
  Returns an `%Ecto.Changeset{}` for tracking repository changes.
  """
  def change_repository(%Repository{} = repository, attrs \\ %{}) do
    Repository.changeset(repository, attrs)
  end

  def by_full_name(full_name) do
    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    Repository
    |> where(full_name: ^full_name)
    |> preload([:source_files])
    |> Repo.one()
    |> case do
      nil -> nil
      repo -> Repo.preload(repo, files: files_query)
    end
  end

  def user_repositories(user, refresh \\ false)

  def user_repositories(user, refresh) do
    organization_id = user.default_organization_id
    user_repositories(user, refresh, organization_id)
  end

  def user_repositories(user, refresh, organization_id) when refresh in [true, :refresh] do
    client = github_api().client(user)

    # Get the organization name for the given organization_id
    organization =
      Repo.get!(Repobot.Accounts.Organization, organization_id) |> Repo.preload(:settings)

    # Check if this is a user organization (org name matches user login)
    is_user_org = organization.name == user.login

    repos_result =
      if is_user_org do
        # For user org (personal account), fetch user repos
        Logger.debug("Fetching repositories for user organization (#{user.login})")
        github_api().user_repos(client, user.login)
      else
        # For regular orgs, fetch org repos
        Logger.debug("Fetching repositories for organization #{organization.name}")
        github_api().list_repos(client, organization.name)
      end

    case repos_result do
      {:ok, repositories} ->
        Logger.debug("Received #{length(repositories)} total repositories")
        Logger.debug("Private repos setting: #{organization.private_repos}")

        # Filter repositories based on private_repos setting
        repos_to_process =
          if organization.private_repos do
            Logger.debug("Private repos enabled, including all repositories")
            repositories
          else
            Logger.debug("Private repos disabled, filtering out private repositories")
            Enum.reject(repositories, & &1["private"])
          end

        Logger.debug("Processing #{length(repos_to_process)} repositories after filtering")

        # Filter repositories based on private_repos setting and ownership
        repos_to_process =
          repos_to_process
          |> Enum.filter(fn repo ->
            # For default org, only include repos owned by the user
            # For other orgs, only include repos owned by the org
            owner = repo["owner"]["login"]

            valid_owner =
              if is_user_org, do: owner == user.login, else: owner == organization.name

            # Apply private repos filter
            valid_owner && (organization.private_repos || !repo["private"])
          end)

        Logger.debug(
          "Private repos count before filtering: #{length(Enum.filter(repos_to_process, & &1["private"]))}"
        )

        # Filter repositories based on private_repos setting
        repos_to_process =
          if organization.private_repos do
            Logger.debug("Private repos enabled, including all repositories")
            repos_to_process
          else
            Logger.debug("Private repos disabled, filtering out private repositories")
            Enum.reject(repos_to_process, & &1["private"])
          end

        Logger.debug(
          "Received #{length(repos_to_process)} total repositories (#{length(Enum.reject(repos_to_process, & &1["private"]))} public)"
        )

        # Create Forks folder
        {:ok, forks_folder} =
          case Repo.get_by(Folder, name: "Forks", organization_id: organization_id) do
            nil ->
              Logger.debug("Creating new Forks folder")

              %Folder{}
              |> Folder.changeset(%{
                name: "Forks",
                organization_id: organization_id
              })
              |> Repo.insert()

            folder ->
              Logger.debug("Using existing Forks folder")
              {:ok, folder}
          end

        # Get or create language folders (for non-fork repositories)
        non_fork_repos = Enum.reject(repos_to_process, & &1["fork"])
        Logger.debug("Processing #{length(non_fork_repos)} non-fork repositories")

        language_folders =
          non_fork_repos
          |> Enum.map(fn repo -> repo["language"] end)
          |> Enum.reject(&is_nil/1)
          |> Enum.uniq()
          |> tap(fn languages ->
            Logger.debug("Found unique languages: #{inspect(languages)}")
          end)
          |> Enum.map(fn language ->
            case Repo.get_by(Folder,
                   name: language,
                   organization_id: organization_id
                 ) do
              nil ->
                Logger.debug("Creating new folder for language: #{language}")

                {:ok, folder} =
                  %Folder{}
                  |> Folder.changeset(%{
                    name: language,
                    organization_id: organization_id
                  })
                  |> Repo.insert()

                folder

              folder ->
                Logger.debug("Using existing folder for language: #{language}")
                folder
            end
          end)
          |> Enum.reduce(%{}, fn folder, acc -> Map.put(acc, folder.name, folder) end)

        # Create/update repositories and assign to appropriate folders
        Logger.debug("Starting to process #{length(repos_to_process)} repositories")

        processed_repos =
          Enum.map(repos_to_process, fn repo ->
            {:ok, repository} =
              case by_full_name_and_organization(repo["full_name"], organization_id) do
                nil ->
                  Logger.debug(
                    "Creating new repository: #{repo["full_name"]} for organization #{organization_id}"
                  )

                  # For new repositories, assign to default folders
                  %Repository{
                    name: repo["name"],
                    owner: repo["owner"]["login"],
                    full_name: repo["full_name"],
                    language: repo["language"],
                    fork: repo["fork"],
                    private: repo["private"],
                    data: repo,
                    organization_id: organization_id,
                    folder_id:
                      cond do
                        repo["fork"] ->
                          forks_folder.id

                        repo["language"] && language_folders[repo["language"]] ->
                          language_folders[repo["language"]].id

                        true ->
                          nil
                      end
                  }
                  |> Repository.changeset()
                  |> Repo.insert()

                %Repository{} = repository ->
                  Logger.debug(
                    "Updating existing repository: #{repo["full_name"]} in organization #{organization_id}"
                  )

                  # For existing repositories in this organization, update the data
                  Repository.changeset(repository, %{
                    data: repo,
                    language: repo["language"],
                    fork: repo["fork"],
                    private: repo["private"]
                  })
                  |> Repo.update()
              end

            # Define custom query for files with content projection
            files_query =
              from(rf in Repobot.RepositoryFile,
                left_join: fc in assoc(rf, :file_content),
                select: %{rf | content: fc.blob}
              )

            repository
            |> Repo.preload([:source_files, :folder])
            |> Repo.preload(files: files_query)
          end)

        Logger.debug("Successfully processed #{length(processed_repos)} repositories")
        processed_repos

      {:error, reason} ->
        Logger.error("Failed to fetch user repositories: #{inspect(reason)}")
        # Return cached repositories on error
        user_repositories(user, false, organization_id)
    end
  end

  def user_repositories(user, false, organization_id) do
    Logger.debug("Fetching cached repositories for user #{user.login}")

    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    repos =
      Repository
      |> where([r], r.organization_id == ^organization_id)
      |> preload([:source_files, :folder])
      |> Repo.all()
      |> Repo.preload(files: files_query)

    Logger.debug("Found #{length(repos)} cached repositories")
    repos
  end

  @doc """
  Associates a source file with a repository.
  """
  def add_source_file(%Repository{} = repository, %SourceFile{} = source_file) do
    %RepositorySourceFile{}
    |> RepositorySourceFile.changeset(%{
      repository_id: repository.id,
      source_file_id: source_file.id
    })
    |> Repo.insert(on_conflict: :nothing)
  end

  @doc """
  Removes the association between a source file and a repository.
  """
  def remove_source_file(%Repository{} = repository, %SourceFile{} = source_file) do
    from(r in RepositorySourceFile,
      where: r.repository_id == ^repository.id and r.source_file_id == ^source_file.id
    )
    |> Repo.delete_all()
  end

  @doc """
  Adds a folder to a template repository's template_folders.
  Only works for repositories where template = true.
  Automatically propagates the template repository's source files to all target repositories in the folder.
  """
  def add_template_folder(%Repository{template: true} = repository, %Folder{} = folder) do
    # Check if the repository is already in this folder using a direct query
    existing_association =
      Repobot.Repo.get_by(Repobot.RepositoryFolder,
        repository_id: repository.id,
        folder_id: folder.id
      )

    if existing_association do
      # Repository is already in this folder, return success
      {:ok, repository}
    else
      # Create a new association directly
      result =
        %Repobot.RepositoryFolder{}
        |> Repobot.RepositoryFolder.changeset(%{
          repository_id: repository.id,
          folder_id: folder.id
        })
        |> Repobot.Repo.insert()

      case result do
        {:ok, _} ->
          # Automatically propagate source files to target repositories in the folder
          propagate_template_source_files_to_folder(repository, folder)
          {:ok, repository}

        {:error, changeset} ->
          {:error, changeset}
      end
    end
  end

  @doc """
  Removes a folder from a template repository's template_folders.
  Only works for repositories where template = true.
  Automatically unsyncs the template repository's source files from all target repositories in the folder.
  """
  def remove_template_folder(%Repository{} = repository, %Folder{} = folder) do
    if repository.template do
      # First, unsync source files from target repositories in the folder
      unsync_template_source_files_from_folder(repository, folder)

      # Then delete the association
      from(rf in Repobot.RepositoryFolder,
        where: rf.repository_id == ^repository.id and rf.folder_id == ^folder.id
      )
      |> Repobot.Repo.delete_all()
      |> case do
        {1, _} -> {:ok, repository}
        # No association found, but that's ok
        {0, _} -> {:ok, repository}
        _ -> {:error, "Failed to remove folder from template repository"}
      end
    else
      {:error, "Only template repositories can be removed from multiple folders"}
    end
  end

  # Propagates all source files from a template repository to all target repositories in a folder.
  # This is called automatically when a template repository is added to a folder.
  defp propagate_template_source_files_to_folder(
         %Repository{template: true} = template_repo,
         %Folder{} = folder
       ) do
    # Define custom query for imported_files with content projection
    imported_files_query =
      from(sf in Repobot.SourceFile,
        left_join: fc in assoc(sf, :file_content),
        select: %{sf | content: fc.blob}
      )

    # Get all source files from the template repository
    template_repo_with_files =
      template_repo
      |> Repo.preload(imported_files: imported_files_query)

    source_files = template_repo_with_files.imported_files

    # Get all target repositories in the folder (excluding template repositories)
    target_repositories = get_target_repositories_in_folder(folder)

    # Associate each source file with each target repository
    Enum.each(source_files, fn source_file ->
      Enum.each(target_repositories, fn target_repo ->
        add_source_file(target_repo, source_file)
      end)
    end)

    Logger.info(
      "Propagated #{length(source_files)} source files from template repository #{template_repo.full_name} to #{length(target_repositories)} target repositories in folder #{folder.name}"
    )
  end

  # Unsyncs all source files from a template repository from all target repositories in a folder.
  # This is called automatically when a template repository is removed from a folder.
  defp unsync_template_source_files_from_folder(
         %Repository{template: true} = template_repo,
         %Folder{} = folder
       ) do
    # Define custom query for imported_files with content projection
    imported_files_query =
      from(sf in Repobot.SourceFile,
        left_join: fc in assoc(sf, :file_content),
        select: %{sf | content: fc.blob}
      )

    # Get all source files from the template repository
    template_repo_with_files =
      template_repo
      |> Repo.preload(imported_files: imported_files_query)

    source_files = template_repo_with_files.imported_files

    # Get all target repositories in the folder (excluding template repositories)
    target_repositories = get_target_repositories_in_folder(folder)

    # Remove the association between each source file and each target repository
    Enum.each(source_files, fn source_file ->
      Enum.each(target_repositories, fn target_repo ->
        remove_source_file(target_repo, source_file)
      end)
    end)

    Logger.info(
      "Unsynced #{length(source_files)} source files from template repository #{template_repo.full_name} from #{length(target_repositories)} target repositories in folder #{folder.name}"
    )
  end

  # Gets all target repositories in a folder (excludes template repositories).
  defp get_target_repositories_in_folder(%Folder{} = folder) do
    folder
    |> Repo.preload(:repositories)
    |> Map.get(:repositories)
    |> Enum.reject(& &1.template)
  end

  defp github_api do
    Application.get_env(:repobot, :github_api)
  end

  def by_full_name_and_organization(full_name, organization_id) do
    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    Repository
    |> where(full_name: ^full_name)
    |> where(organization_id: ^organization_id)
    |> preload([:source_files])
    |> Repo.one()
    |> case do
      nil -> nil
      repo -> Repo.preload(repo, files: files_query)
    end
  end

  @doc """
  Returns the list of repositories for a given organization.
  """
  def list_repositories_for_organization(organization) do
    from(r in Repository, where: r.organization_id == ^organization.id, preload: [:folder])
    |> Repo.all()
  end

  @doc """
  Returns the list of repositories matching the given IDs.
  Preloads the folder and files for complete repository data.
  """
  def list_repositories_by_ids(ids) do
    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    from(r in Repository, where: r.id in ^ids, preload: [:folder])
    |> Repo.all()
    |> Repo.preload(files: files_query)
  end

  @doc """
  Returns a repository matching the given options.
  """
  def get_repository_by(opts) do
    Repo.get_by(Repository, opts)
  end

  @doc """
  Returns a *template* repository matching the given options.
  """
  def get_template_repository_by(opts) do
    case Repo.get_by(Repository, Keyword.put(opts, :template, true)) do
      nil -> {:error, :not_found}
      repo -> {:ok, repo}
    end
  end

  @doc """
  Finds a repository by GitHub repository ID stored in the data field.
  """
  def get_repository_by_github_id(github_id) when is_integer(github_id) do
    # Define custom query for imported_files with content projection
    imported_files_query =
      from(sf in Repobot.SourceFile,
        left_join: fc in assoc(sf, :file_content),
        select: %{sf | content: fc.blob}
      )

    # Define custom query for files with content projection
    files_query =
      from(rf in Repobot.RepositoryFile,
        left_join: fc in assoc(rf, :file_content),
        select: %{rf | content: fc.blob}
      )

    Repository
    |> where([r], fragment("?->>'id' = ?", r.data, ^to_string(github_id)))
    |> preload([:source_files, :template_folders])
    |> Repo.one()
    |> case do
      nil ->
        nil

      repository ->
        repository
        |> Repo.preload(files: files_query)
        |> Repo.preload(imported_files: imported_files_query)
    end
  end

  @doc """
  Deletes a repository and all associated data with proper cleanup.
  This function handles the deletion of:
  - Repository files (handled by database cascade)
  - Source file associations (handled by schema on_replace)
  - Template folder associations (handled by schema on_replace)
  - Events related to the repository (repository_id set to nil by database foreign key constraint)
  """
  def delete_repository_with_cleanup(%Repository{} = repository) do
    Logger.info("Starting repository deletion with cleanup",
      repository_id: repository.id,
      full_name: repository.full_name,
      github_id: get_in(repository.data, ["id"])
    )

    # Events are preserved for audit purposes - the database foreign key constraint
    # will automatically set repository_id to nil when the repository is deleted

    # Delete the repository (cascades will handle files, associations are handled by schema)
    case Repo.delete(repository) do
      {:ok, deleted_repository} ->
        Logger.info("Repository successfully deleted",
          repository_id: deleted_repository.id,
          full_name: deleted_repository.full_name,
          github_id: get_in(deleted_repository.data, ["id"])
        )

        {:ok, deleted_repository}

      {:error, changeset} ->
        Logger.error("Failed to delete repository",
          repository_id: repository.id,
          full_name: repository.full_name,
          errors: changeset.errors
        )

        {:error, changeset}
    end
  end

  @doc """
  Updates a repository from GitHub webhook data.
  This function compares the old and new repository data and updates
  the repository with the new information from GitHub.
  """
  def update_repository_from_webhook(%Repository{} = repository, github_repo_data) do
    old_name = repository.name
    old_full_name = repository.full_name
    old_owner = repository.owner
    old_private = repository.private
    old_description = get_in(repository.data, ["description"])

    new_name = github_repo_data["name"]
    new_full_name = github_repo_data["full_name"]
    new_owner = github_repo_data["owner"]["login"]
    new_private = github_repo_data["private"]
    new_description = github_repo_data["description"]

    # Log the changes being made
    changes = []
    changes = if old_name != new_name, do: [{"name", old_name, new_name} | changes], else: changes

    changes =
      if old_full_name != new_full_name,
        do: [{"full_name", old_full_name, new_full_name} | changes],
        else: changes

    changes =
      if old_owner != new_owner, do: [{"owner", old_owner, new_owner} | changes], else: changes

    changes =
      if old_private != new_private,
        do: [{"private", old_private, new_private} | changes],
        else: changes

    changes =
      if old_description != new_description,
        do: [{"description", old_description, new_description} | changes],
        else: changes

    Logger.info("Updating repository from webhook",
      repository_id: repository.id,
      github_id: github_repo_data["id"],
      old_full_name: old_full_name,
      new_full_name: new_full_name,
      changes:
        Enum.map(changes, fn {field, old_val, new_val} ->
          %{field: field, old: old_val, new: new_val}
        end)
    )

    # Update repository with new data
    update_attrs = %{
      name: new_name,
      full_name: new_full_name,
      owner: new_owner,
      private: new_private,
      language: github_repo_data["language"],
      fork: github_repo_data["fork"],
      data: github_repo_data
    }

    case update_repository(repository, update_attrs) do
      {:ok, updated_repository} ->
        Logger.info("Repository successfully updated from webhook",
          repository_id: updated_repository.id,
          github_id: github_repo_data["id"],
          old_full_name: old_full_name,
          new_full_name: updated_repository.full_name,
          changes_count: length(changes)
        )

        {:ok, updated_repository}

      {:error, changeset} ->
        Logger.error("Failed to update repository from webhook",
          repository_id: repository.id,
          github_id: github_repo_data["id"],
          old_full_name: old_full_name,
          new_full_name: new_full_name,
          errors: changeset.errors
        )

        {:error, changeset}
    end
  end
end
